import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import os


def analyze_feature_scales(folder_path, behavioral_features, output_dir="feature_analysis"):
    """
    分析不同特征的数量级差异和分布特征
    
    参数:
        folder_path (str): 包含CSV文件的文件夹路径
        behavioral_features (list): 要分析的特征列表
        output_dir (str): 输出目录
    
    返回:
        dict: 包含各特征统计信息的字典
    """
    
    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)
    
    print("🔍 开始分析特征尺度差异...")
    
    # 收集所有特征数据
    all_feature_data = {feature: [] for feature in behavioral_features}
    file_count = 0
    
    # 遍历所有CSV文件
    for filename in os.listdir(folder_path):
        if not filename.endswith('.csv'):
            continue
            
        try:
            file_path = os.path.join(folder_path, filename)
            df = pd.read_csv(file_path)
            
            # 检查是否包含所需特征
            missing_features = [f for f in behavioral_features if f not in df.columns]
            if missing_features:
                print(f"⚠️ 文件 {filename} 缺少特征: {missing_features}")
                continue
            
            # 收集特征数据
            for feature in behavioral_features:
                feature_values = df[feature].dropna().values
                all_feature_data[feature].extend(feature_values)
            
            file_count += 1
            
        except Exception as e:
            print(f"❌ 读取文件 {filename} 失败: {e}")
    
    print(f"✅ 成功处理 {file_count} 个文件")
    
    # 计算统计信息
    feature_stats = {}
    scale_ratios = {}
    
    print("\n📊 特征统计分析:")
    print("=" * 80)
    print(f"{'特征名称':<15} {'均值':<12} {'标准差':<12} {'最小值':<12} {'最大值':<12} {'数量级':<8}")
    print("=" * 80)
    
    for feature in behavioral_features:
        data = np.array(all_feature_data[feature])
        
        if len(data) == 0:
            print(f"⚠️ 特征 {feature} 没有有效数据")
            continue
        
        stats = {
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'median': np.median(data),
            'q25': np.percentile(data, 25),
            'q75': np.percentile(data, 75),
            'count': len(data)
        }
        
        feature_stats[feature] = stats
        
        # 计算数量级
        magnitude = int(np.log10(abs(stats['mean']) + 1e-10))
        
        print(f"{feature:<15} {stats['mean']:<12.4f} {stats['std']:<12.4f} "
              f"{stats['min']:<12.4f} {stats['max']:<12.4f} {magnitude:<8}")
    
    # 分析尺度差异
    print("\n🔍 特征间尺度差异分析:")
    print("=" * 60)
    
    means = [feature_stats[f]['mean'] for f in behavioral_features if f in feature_stats]
    stds = [feature_stats[f]['std'] for f in behavioral_features if f in feature_stats]
    
    if len(means) > 1:
        mean_ratio = max(means) / min(means) if min(means) != 0 else float('inf')
        std_ratio = max(stds) / min(stds) if min(stds) != 0 else float('inf')
        
        print(f"均值最大差异倍数: {mean_ratio:.2f}")
        print(f"标准差最大差异倍数: {std_ratio:.2f}")
        
        if mean_ratio > 10 or std_ratio > 10:
            print("⚠️ 检测到显著的尺度差异！建议使用特征级标准化")
        else:
            print("✅ 特征尺度相对均衡")
    
    # 生成可视化
    create_feature_visualizations(all_feature_data, behavioral_features, output_dir)
    
    # 保存统计信息
    stats_file = os.path.join(output_dir, "feature_statistics.json")
    with open(stats_file, 'w') as f:
        json.dump(feature_stats, f, indent=2)
    
    print(f"\n💾 统计信息已保存到: {stats_file}")
    
    return feature_stats


def create_feature_visualizations(all_feature_data, behavioral_features, output_dir):
    """
    创建特征分布和尺度对比的可视化图表
    """
    
    print("\n📈 生成可视化图表...")
    
    # 1. 特征分布直方图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, feature in enumerate(behavioral_features):
        if i >= len(axes):
            break
            
        data = all_feature_data[feature]
        if len(data) == 0:
            continue
            
        axes[i].hist(data, bins=50, alpha=0.7, edgecolor='black')
        axes[i].set_title(f'{feature} 分布')
        axes[i].set_xlabel('值')
        axes[i].set_ylabel('频次')
        axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "feature_distributions.png"), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 特征尺度对比（箱线图）
    plt.figure(figsize=(12, 8))
    
    # 准备数据
    plot_data = []
    labels = []
    
    for feature in behavioral_features:
        data = all_feature_data[feature]
        if len(data) > 0:
            plot_data.append(data)
            labels.append(feature)
    
    if plot_data:
        plt.boxplot(plot_data, labels=labels)
        plt.title('特征尺度对比 (箱线图)')
        plt.ylabel('值')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "feature_scale_comparison.png"), dpi=300, bbox_inches='tight')
        plt.close()
    
    # 3. 标准化前后对比
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 标准化前
    means_before = [np.mean(all_feature_data[f]) for f in behavioral_features if len(all_feature_data[f]) > 0]
    stds_before = [np.std(all_feature_data[f]) for f in behavioral_features if len(all_feature_data[f]) > 0]
    valid_features = [f for f in behavioral_features if len(all_feature_data[f]) > 0]
    
    x_pos = np.arange(len(valid_features))
    
    ax1.bar(x_pos - 0.2, means_before, 0.4, label='均值', alpha=0.7)
    ax1.bar(x_pos + 0.2, stds_before, 0.4, label='标准差', alpha=0.7)
    ax1.set_title('标准化前')
    ax1.set_xlabel('特征')
    ax1.set_ylabel('值')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(valid_features, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 标准化后（理论值）
    means_after = [0] * len(valid_features)  # 标准化后均值为0
    stds_after = [1] * len(valid_features)   # 标准化后标准差为1
    
    ax2.bar(x_pos - 0.2, means_after, 0.4, label='均值', alpha=0.7)
    ax2.bar(x_pos + 0.2, stds_after, 0.4, label='标准差', alpha=0.7)
    ax2.set_title('特征级标准化后')
    ax2.set_xlabel('特征')
    ax2.set_ylabel('值')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(valid_features, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "normalization_comparison.png"), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 可视化图表已保存到: {output_dir}")


def recommend_normalization_strategy(feature_stats):
    """
    基于特征统计信息推荐标准化策略
    """
    
    print("\n💡 标准化策略建议:")
    print("=" * 50)
    
    if not feature_stats:
        print("❌ 没有有效的特征统计信息")
        return
    
    # 计算尺度差异
    means = [stats['mean'] for stats in feature_stats.values()]
    stds = [stats['std'] for stats in feature_stats.values()]
    
    mean_ratio = max(means) / min(means) if min(means) != 0 else float('inf')
    std_ratio = max(stds) / min(stds) if min(stds) != 0 else float('inf')
    
    print(f"均值差异倍数: {mean_ratio:.2f}")
    print(f"标准差差异倍数: {std_ratio:.2f}")
    
    if mean_ratio > 100 or std_ratio > 100:
        print("🔴 强烈建议: 使用特征级标准化 (Z-score normalization)")
        print("   原因: 特征间存在极大的尺度差异")
    elif mean_ratio > 10 or std_ratio > 10:
        print("🟡 建议: 使用特征级标准化")
        print("   原因: 特征间存在显著的尺度差异")
    else:
        print("🟢 可选: 全局标准化或特征级标准化都可以")
        print("   原因: 特征尺度相对均衡")
    
    print("\n📋 实施建议:")
    print("1. 在数据加载时对每个特征分别计算均值和标准差")
    print("2. 对训练、验证、测试数据使用相同的标准化参数")
    print("3. 保存标准化参数用于推理阶段")
    print("4. 监控标准化后各特征的分布是否合理")


if __name__ == "__main__":
    # 示例用法
    folder_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train"
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
    
    # 分析特征尺度
    stats = analyze_feature_scales(folder_path, behavioral_features)
    
    # 推荐标准化策略
    recommend_normalization_strategy(stats)
