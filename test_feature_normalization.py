#!/usr/bin/env python3
"""
测试特征级标准化的效果
"""

import sys
import os
import json
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.data_loader import get_source_data
from utilities.feature_analysis import analyze_feature_scales, recommend_normalization_strategy


def test_feature_normalization():
    """
    测试特征级标准化的效果
    """
    
    print("🧪 测试特征级标准化效果")
    print("=" * 60)
    
    # 配置参数
    config = {
        "behavioral_features": ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"],
        "train_folder_path": "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train",
        "test_folder_path": "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation",
        "label_file": "/data/gsd/czx/project/Learning_status_detection/TCCT_Net-main/train_engagement_labels.csv"
    }
    
    # 1. 首先分析原始数据的特征尺度
    print("\n📊 步骤1: 分析原始数据特征尺度")
    print("-" * 40)
    
    try:
        stats = analyze_feature_scales(
            config["train_folder_path"], 
            config["behavioral_features"],
            output_dir="feature_analysis_results"
        )
        
        # 推荐标准化策略
        recommend_normalization_strategy(stats)
        
    except Exception as e:
        print(f"❌ 特征分析失败: {e}")
        return False
    
    # 2. 测试数据加载和标准化
    print("\n🔧 步骤2: 测试改进的数据加载和标准化")
    print("-" * 40)
    
    try:
        # 加载数据（使用改进的标准化方法）
        train_data, train_labels, test_data, test_labels = get_source_data(
            config["train_folder_path"],
            config["test_folder_path"], 
            config["label_file"],
            config["behavioral_features"]
        )
        
        print(f"✅ 数据加载成功")
        print(f"   训练数据形状: {train_data.shape}")
        print(f"   测试数据形状: {test_data.shape}")
        
        # 3. 验证标准化效果
        print("\n📈 步骤3: 验证标准化效果")
        print("-" * 40)
        
        verify_normalization_effect(train_data, config["behavioral_features"])
        
        # 4. 检查标准化统计文件
        print("\n📁 步骤4: 检查标准化统计文件")
        print("-" * 40)
        
        if os.path.exists("feature_normalization_stats.json"):
            with open("feature_normalization_stats.json", "r") as f:
                norm_stats = json.load(f)
            
            print("✅ 标准化统计文件存在")
            print("📋 保存的特征统计信息:")
            for feature, stats in norm_stats.items():
                print(f"   {feature}: mean={stats['mean']:.6f}, std={stats['std']:.6f}")
        else:
            print("❌ 标准化统计文件不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_normalization_effect(data, behavioral_features):
    """
    验证标准化后的效果
    
    参数:
        data: 标准化后的数据 (samples, channels, features, time_steps)
        behavioral_features: 特征名称列表
    """
    
    print("🔍 验证标准化后各特征的统计特性:")
    
    samples, channels, features, time_steps = data.shape
    
    print(f"{'特征名称':<15} {'均值':<12} {'标准差':<12} {'最小值':<12} {'最大值':<12}")
    print("-" * 65)
    
    all_means = []
    all_stds = []
    
    for i, feature_name in enumerate(behavioral_features):
        # 提取该特征的所有数据
        feature_data = data[:, :, i, :].flatten()
        
        mean_val = np.mean(feature_data)
        std_val = np.std(feature_data)
        min_val = np.min(feature_data)
        max_val = np.max(feature_data)
        
        all_means.append(abs(mean_val))
        all_stds.append(std_val)
        
        print(f"{feature_name:<15} {mean_val:<12.6f} {std_val:<12.6f} "
              f"{min_val:<12.6f} {max_val:<12.6f}")
    
    # 检查标准化质量
    print("\n📊 标准化质量评估:")
    
    # 检查均值是否接近0
    max_mean = max(all_means)
    if max_mean < 0.01:
        print(f"✅ 均值检查通过: 最大绝对均值 = {max_mean:.6f}")
    else:
        print(f"⚠️ 均值检查警告: 最大绝对均值 = {max_mean:.6f} (期望 < 0.01)")
    
    # 检查标准差是否接近1
    std_range = max(all_stds) - min(all_stds)
    if std_range < 0.1:
        print(f"✅ 标准差检查通过: 标准差范围 = {std_range:.6f}")
    else:
        print(f"⚠️ 标准差检查警告: 标准差范围 = {std_range:.6f} (期望 < 0.1)")
    
    # 检查特征间尺度是否均衡
    mean_ratio = max(all_means) / (min(all_means) + 1e-10)
    std_ratio = max(all_stds) / (min(all_stds) + 1e-10)
    
    print(f"📏 特征间尺度均衡性:")
    print(f"   均值比例: {mean_ratio:.2f}")
    print(f"   标准差比例: {std_ratio:.2f}")
    
    if mean_ratio < 10 and std_ratio < 2:
        print("✅ 特征尺度已很好地均衡化")
    else:
        print("⚠️ 特征尺度仍存在一定差异")


def compare_with_global_normalization():
    """
    比较特征级标准化与全局标准化的效果
    """
    
    print("\n🔄 比较特征级标准化与全局标准化")
    print("=" * 50)
    
    # 这里可以添加对比测试的代码
    # 由于时间限制，暂时跳过详细实现
    print("💡 建议: 可以通过训练两个模型来比较效果")
    print("   1. 使用特征级标准化训练的模型")
    print("   2. 使用全局标准化训练的模型")
    print("   3. 比较两者的收敛速度和最终性能")


if __name__ == "__main__":
    print("🚀 开始测试特征标准化改进")
    
    # 测试特征标准化
    success = test_feature_normalization()
    
    if success:
        print("\n🎉 测试完成！特征级标准化实现成功")
        print("\n📋 后续建议:")
        print("1. 运行完整训练来验证性能提升")
        print("2. 监控训练过程中的损失收敛情况")
        print("3. 比较与原始方法的准确率差异")
        print("4. 检查特征融合模块的注意力权重分布")
    else:
        print("\n❌ 测试失败，请检查错误信息并修复")
    
    print("\n" + "="*60)
