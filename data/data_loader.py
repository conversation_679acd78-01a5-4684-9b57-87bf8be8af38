import os  # 导入 os 模块，用于处理文件路径和目录
import numpy as np  # 导入 numpy 库，用于数值计算
import pandas as pd  # 导入 pandas 库，用于数据处理
from tqdm import tqdm  # 导入 tqdm 库，用于显示进度条
import json

def load_csv_data(folder_path, label_file, behavioral_features, exclude_list_path=None):
    """
    从文件夹中的 CSV 文件加载数据及其对应的标签。

    参数:
        folder_path (str): 包含 CSV 文件的文件夹路径。
        label_file (str): 包含标签的 CSV 文件路径。
        behavioral_features (list of str): 需要从数据中提取的行为特征列表。

    返回:
        np.array: 从 CSV 文件中提取的数据数组。
        np.array: 与数据对应的标签数组。
    """

    # 标签映射
    label_mapping = {
        "Not-Engaged": 0,
        "Barely-engaged": 1,
        "Engaged": 2,
        "Highly-Engaged": 3
    }
    #新加的异常数据判断
    exclude_set = set()
    if exclude_list_path:
        if os.path.exists(exclude_list_path):
            exclude_df = pd.read_csv(exclude_list_path)
            exclude_set = set(exclude_df["文件"].values)
            print(f"🛑 共有 {len(exclude_set)} 个文件将被排除")
        else:
            print(f"⚠️ 排除文件路径不存在，跳过排除逻辑: {exclude_list_path}")



    labels_df = pd.read_csv(label_file)  # 读取标签文件
    all_data, all_labels = [], []  # 初始化存储数据和标签的列表

    # 遍历文件夹中的每个 CSV 文件
    for filename in tqdm(os.listdir(folder_path), desc="Loading data"):
        if filename.endswith('.csv'):  # 检查文件是否为 CSV 文件
             # ✅ 新增：跳过异常数据
            if filename in exclude_set:
                continue

            subject_id = filename.split('.')[0]  # 提取文件名中的主题 ID
            subject_file = os.path.join(folder_path, filename)  # 构建完整的文件路径
            subject_data = pd.read_csv(subject_file)  # 读取 CSV 文件中的数据

            # 提取选定的行为特征并堆叠数据
            subject_data_values = np.stack([subject_data[col].values for col in behavioral_features], axis=0)
            subject_label = labels_df[labels_df['chunk'].str.contains(subject_id)]['label'].values  # 提取对应的标签

            # 如果标签存在，则将数据和标签添加到列表中
            if len(subject_label) > 0:
                label_str = subject_label[0]
                if label_str == "SNP":
                    continue  # 跳过 Subject Not Present 标签
                if label_str in label_mapping:
                     ##### ✅ 在此处插入 NaN 检查和日志写入
                    if np.isnan(subject_data_values).any():
                        with open("nan_subjects_log.txt", "a") as log_file:
                            log_file.write(f"NaN detected in subject: {subject_id}, label: {label_str}\n")

                    all_data.append(subject_data_values)
                    all_labels.append(label_mapping[label_str])
                else:
                    print(f"Unrecognized label '{label_str}' for subject {subject_id}")
            else:
                print(f"No label found for subject {subject_id}")  # 如果没有找到标签，则打印警告

    # 将收集的数据和标签转换为 numpy 数组
    all_data = np.array(all_data)
    all_data = np.expand_dims(all_data, axis=1)  # 扩展数据维度
    all_labels = np.array(all_labels)

    return all_data, all_labels  # 返回数据和标签
 

def get_source_data(train_folder_path, test_folder_path, label_file, behavioral_features, use_feature_normalization=True):
    """
    从指定文件夹加载并预处理训练和测试数据。

    参数:
        train_folder_path (str): 包含训练数据的文件夹路径。
        test_folder_path (str): 包含测试数据的文件夹路径。
        label_file (str): 包含标签的 CSV 文件路径。
        behavioral_features (list of str): 需要从数据中提取的行为特征列表。

    返回:
        np.array: 处理后的训练数据。
        np.array: 训练数据的标签。
        np.array: 处理后的测试数据。
        np.array: 测试数据的标签。
    """

    # 加载训练数据 - 优先使用Isolation Forest结果
    print('\nLoading train data ...')
    # 按优先级排序的异常文件候选列表
    train_exclude_candidates = [
        "train_isolation_outliers.csv",  # 仅Isolation Forest结果 (推荐)
        "data/train_isolation_outliers.csv",  # data目录下的Isolation Forest结果
        "data/train_outliers.csv",  # 新的二次筛选结果
        "train_outliers.csv",  # 根目录下的二次筛选结果
        "data/train_combined_outliers.csv",  # 组合筛选结果
        "train_combined_outliers.csv",  # 根目录下的组合结果
        "data/train_kmeans_outliers.csv",  # data目录下的K-means结果
        "train_kmeans_outliers.csv",  # 根目录下的K-means结果
        "/data/gsd/ywj/project/TCCT_Net-main_Improvement0/data/train_kmeans_outliers.csv"  # 绝对路径原始结果
    ]

    train_exclude_path = None
    for path in train_exclude_candidates:
        if os.path.exists(path):
            train_exclude_path = path
            break

    if train_exclude_path:
        if "isolation" in train_exclude_path:
            print(f"✅ 使用Isolation Forest异常检测结果: {train_exclude_path}")
        elif "train_outliers.csv" in train_exclude_path:
            print(f"✅ 使用K-means+Isolation Forest二次筛选结果: {train_exclude_path}")
        elif "combined" in train_exclude_path:
            print(f"✅ 使用组合筛选结果: {train_exclude_path}")
        else:
            print(f"⚠️ 使用K-means结果: {train_exclude_path}")
    else:
        print("⚠️ 未找到异常文件列表，将加载所有数据")

    train_data, train_labels = load_csv_data(train_folder_path, label_file, behavioral_features, exclude_list_path=train_exclude_path)
    train_labels = train_labels.reshape(1, -1)  # 调整标签形状

    # 打乱训练数据
    shuffle_index = np.random.permutation(len(train_data))
    train_data = train_data[shuffle_index, :, :, :]
    train_labels = train_labels[0][shuffle_index]

    # 加载测试数据 - 优先使用Isolation Forest结果
    print('\nLoading test data ...')
    # 按优先级排序的异常文件候选列表
    val_exclude_candidates = [
        "val_isolation_outliers.csv",  # 仅Isolation Forest结果 (推荐)
        "data/val_isolation_outliers.csv",  # data目录下的Isolation Forest结果
        "data/val_outliers.csv",  # 新的二次筛选结果
        "val_outliers.csv",  # 根目录下的二次筛选结果
        "data/val_combined_outliers.csv",  # 组合筛选结果
        "val_combined_outliers.csv",  # 根目录下的组合结果
        "data/val_kmeans_outliers.csv",  # data目录下的K-means结果
        "val_kmeans_outliers.csv",  # 根目录下的K-means结果
        "/data/gsd/ywj/project/TCCT_Net-main_Improvement0/data/val_kmeans_outliers.csv"  # 绝对路径原始结果
    ]

    val_exclude_path = None
    for path in val_exclude_candidates:
        if os.path.exists(path):
            val_exclude_path = path
            break

    if val_exclude_path:
        if "isolation" in val_exclude_path:
            print(f"✅ 使用Isolation Forest异常检测结果: {val_exclude_path}")
        elif "val_outliers.csv" in val_exclude_path:
            print(f"✅ 使用K-means+Isolation Forest二次筛选结果: {val_exclude_path}")
        elif "combined" in val_exclude_path:
            print(f"✅ 使用组合筛选结果: {val_exclude_path}")
        else:
            print(f"⚠️ 使用K-means结果: {val_exclude_path}")
    else:
        print("⚠️ 未找到异常文件列表，将加载所有数据")

    test_data, test_labels = load_csv_data(test_folder_path, label_file, behavioral_features, exclude_list_path=val_exclude_path)
    test_labels = test_labels.reshape(-1)  # 调整标签形状

    ###################清洗训练数据中的非法值（NaN 或 Inf），替换为 0.0
    # train_data = np.nan_to_num(train_data, nan=target_mean, posinf=target_mean, neginf=target_mean)
    # test_data = np.nan_to_num(test_data, nan=target_mean, posinf=target_mean, neginf=target_mean)
    # train_data = np.nan_to_num(train_data, nan=0.0, posinf=0.0, neginf=0.0)
    # test_data = np.nan_to_num(test_data, nan=0.0, posinf=0.0, neginf=0.0)
    ###################清洗测试数据中的非法值
    
    # === 新增保存CSV的代码 ===
    # 将四维数据展平为二维 (样本数, 特征*时间步)
    samples, channels, features, time_steps = train_data.shape
    flattened_data = train_data.reshape(samples, -1)  # 形状变为 (7415, 1 * 2 * 280) = (7415, 560)

    # 生成列名（包含特征和时间步信息）
    column_names = []
    for t in range(time_steps):
        for feature in behavioral_features:
            column_names.append(f"{feature}_t{t+1}")  # 例如: gaze_0_x_t1, AU01_r_t1,...gaze_0_x_t280

    # 创建DataFrame并添加标签
    df_train = pd.DataFrame(flattened_data, columns=column_names)
    df_train["label"] = train_labels  # 添加标签列

    # 保存到CSV（文件大小约7415行×561列）
    df_train.to_csv("train_data_processed.csv", index=False, float_format="%.4f")  # 保留4位小数
    print(f"\nSaved train_data to train_data_processed.csv (shape: {df_train.shape})")
    # === 新增代码结束 ===

    # 使用训练数据的统计量标准化训练和测试数据
    # 改进：按特征分别标准化，解决不同特征间的数量级差异问题

    # 保存标准化前的统计信息
    print("&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&")
    print("train_data shape:", train_data.shape)
    print("train_labels shape:", train_labels.shape)

    # 分析每个特征的统计信息
    print("\n📊 各特征标准化前的统计信息:")
    samples, channels, features, time_steps = train_data.shape
    feature_stats = {}

    for i, feature_name in enumerate(behavioral_features):
        feature_data = train_data[:, :, i, :].flatten()  # 展平该特征的所有数据
        feature_mean = np.mean(feature_data)
        feature_std = np.std(feature_data)
        feature_stats[feature_name] = {'mean': feature_mean, 'std': feature_std}
        print(f"  {feature_name}: mean={feature_mean:.6f}, std={feature_std:.6f}")

    # 根据参数选择标准化策略
    if use_feature_normalization:
        print("\n🔧 执行改进的特征标准化...")
        train_data_normalized = train_data.copy()
        test_data_normalized = test_data.copy()

        # 分析特征类型并采用不同策略
        pose_features = [f for f in behavioral_features if f.startswith('pose_')]
        au_features = [f for f in behavioral_features if f.startswith('AU')]

        print(f"📍 Pose特征: {pose_features}")
        print(f"😊 AU特征: {au_features}")

        for i, feature_name in enumerate(behavioral_features):
            feature_mean = feature_stats[feature_name]['mean']
            feature_std = feature_stats[feature_name]['std']

            # 避免除零错误
            if feature_std < 1e-8:
                print(f"⚠️ 警告: {feature_name} 的标准差过小 ({feature_std}), 跳过标准化")
                continue

            # 根据特征类型选择标准化策略
            if feature_name in pose_features:
                # Pose特征：适度标准化，保留相对尺度信息
                scale_factor = 0.5  # 适度标准化
                train_data_normalized[:, :, i, :] = (train_data[:, :, i, :] - feature_mean) / feature_std * scale_factor
                test_data_normalized[:, :, i, :] = (test_data[:, :, i, :] - feature_mean) / feature_std * scale_factor
                print(f"  📍 {feature_name}: 适度标准化 (scale={scale_factor})")
            else:
                # AU特征：保守标准化，保留精细变化模式
                # 计算相对于pose特征的尺度比例，避免过度拉伸
                pose_std_avg = np.mean([feature_stats[f]['std'] for f in pose_features])
                au_scale_factor = min(0.2, feature_std / pose_std_avg * 0.5)  # 限制最大缩放

                train_data_normalized[:, :, i, :] = (train_data[:, :, i, :] - feature_mean) / feature_std * au_scale_factor
                test_data_normalized[:, :, i, :] = (test_data[:, :, i, :] - feature_mean) / feature_std * au_scale_factor
                print(f"  😊 {feature_name}: 保守标准化 (scale={au_scale_factor:.3f})")
    else:
        print("\n🔧 使用传统全局标准化...")
        # 回退到原来的全局标准化方法
        target_mean = np.mean(train_data)
        target_std = np.std(train_data)
        train_data_normalized = (train_data - target_mean) / target_std
        test_data_normalized = (test_data - target_mean) / target_std
        print(f"全局标准化: mean={target_mean:.6f}, std={target_std:.6f}")

    # 验证标准化效果
    print("\n📊 各特征标准化后的统计信息:")
    for i, feature_name in enumerate(behavioral_features):
        feature_data = train_data_normalized[:, :, i, :].flatten()
        feature_mean = np.mean(feature_data)
        feature_std = np.std(feature_data)
        print(f"  {feature_name}: mean={feature_mean:.6f}, std={feature_std:.6f}")

    # 更新数据
    train_data = train_data_normalized
    test_data = test_data_normalized

    # 保存特征统计信息供后续使用
    import json
    with open("feature_normalization_stats.json", "w") as f:
        json.dump(feature_stats, f, indent=2)
    print("✅ 特征标准化统计信息已保存到 feature_normalization_stats.json")

    # 保存标准化后的前5个训练样本为 JSON
    with open("train_data_sample.json", "w") as f:
        json.dump(train_data[:5].tolist(), f)

    # 为了兼容性，计算全局统计量（但实际上已经不需要了）
    target_mean = np.mean(train_data)
    target_std = np.std(train_data)
    print(f"\n全局统计量（仅供参考）: target_mean={target_mean:.6f}, target_std={target_std:.6f}")


    print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
    print("test_data shape:", test_data.shape)
    print("test_labels shape:", test_labels.shape)



    return train_data, train_labels, test_data, test_labels  # 返回处理后的数据和标签


def get_source_data_inference(inference_folder_path, label_file_inference,
                              behavioral_features, feature_stats=None):
    """
    从指定文件夹加载并预处理推理数据。

    参数:
        inference_folder_path (str): 包含推理数据的文件夹路径。
        label_file_inference (str): 包含标签的 CSV 文件路径。
        behavioral_features (list of str): 需要从数据中提取的行为特征列表。
        feature_stats (dict): 特征标准化统计信息，如果为None则从文件加载。

    返回:
        np.array: 处理后的推理数据。
        np.array: 推理数据的标签。
    """

    # 加载推理数据
    print('\nLoading data for inference ...')
    inference_data, inference_labels = load_csv_data(inference_folder_path, label_file_inference, behavioral_features)
    inference_labels = inference_labels.reshape(-1)  # 调整标签形状

    # 加载特征标准化统计信息
    if feature_stats is None:
        try:
            import json
            with open("feature_normalization_stats.json", "r") as f:
                feature_stats = json.load(f)
            print("✅ 已加载特征标准化统计信息")
        except FileNotFoundError:
            print("❌ 未找到特征标准化统计信息文件，使用全局标准化")
            # 回退到全局标准化（兼容旧版本）
            target_mean = np.mean(inference_data)
            target_std = np.std(inference_data)
            inference_data = (inference_data - target_mean) / target_std
            return inference_data, inference_labels

    # 使用特征级标准化
    print("🔧 对推理数据执行按特征分别标准化...")
    inference_data_normalized = inference_data.copy()

    for i, feature_name in enumerate(behavioral_features):
        if feature_name in feature_stats:
            feature_mean = feature_stats[feature_name]['mean']
            feature_std = feature_stats[feature_name]['std']

            # 避免除零错误
            if feature_std < 1e-8:
                print(f"⚠️ 警告: {feature_name} 的标准差过小，跳过标准化")
                continue

            # 对推理数据的该特征进行标准化
            inference_data_normalized[:, :, i, :] = (inference_data[:, :, i, :] - feature_mean) / feature_std
            print(f"  ✅ {feature_name}: 已标准化")
        else:
            print(f"⚠️ 警告: 未找到 {feature_name} 的标准化统计信息")

    return inference_data_normalized, inference_labels  # 返回处理后的推理数据和标签