#!/usr/bin/env python3
"""
快速调整contamination参数的脚本
"""

import os
import re

def update_contamination(new_contamination):
    """
    更新配置文件中的contamination参数
    
    参数:
        new_contamination: 新的contamination值 (0.01-0.15)
    """
    config_file = "outlier_detection_config.py"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    # 验证参数范围
    if not (0.01 <= new_contamination <= 0.15):
        print(f"❌ contamination参数应在0.01-0.15之间，当前值: {new_contamination}")
        return False
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式替换contamination值
        pattern = r"('contamination':\s*)([0-9.]+)"
        replacement = f"\\g<1>{new_contamination}"
        new_content = re.sub(pattern, replacement, content)
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 已更新contamination参数为: {new_contamination}")
        print(f"📄 配置文件已更新: {config_file}")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False

def show_contamination_guide():
    """显示contamination参数调整指南"""
    print("📋 Contamination参数调整指南")
    print("=" * 50)
    print("contamination值越小，筛选的异常样本越少（更严格）")
    print("contamination值越大，筛选的异常样本越多（更宽松）")
    print()
    print("推荐值:")
    print("  0.01 (1%)  - 非常严格，只筛选最明显的异常")
    print("  0.02 (2%)  - 严格")
    print("  0.03 (3%)  - 较严格")
    print("  0.04 (4%)  - 适中")
    print("  0.05 (5%)  - 稍宽松")
    print("  0.06 (6%)  - 宽松 (当前默认)")
    print("  0.08 (8%)  - 较宽松")
    print("  0.10 (10%) - 很宽松，筛选较多异常")
    print("=" * 50)

def main():
    """主函数"""
    print("🔧 Contamination参数快速调整工具")
    print("=" * 40)
    
    show_contamination_guide()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 设置新的contamination值")
            print("2. 查看当前配置")
            print("3. 退出")
            
            choice = input("请选择 (1/2/3): ").strip()
            
            if choice == "1":
                contamination_str = input("请输入新的contamination值 (0.01-0.15): ").strip()
                try:
                    contamination = float(contamination_str)
                    if update_contamination(contamination):
                        print(f"\n💡 下次运行异常检测时将使用新参数: {contamination}")
                        print("💡 建议运行: python run_outlier_detection.py")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif choice == "2":
                # 显示当前配置
                try:
                    from outlier_detection_config import print_config_summary
                    print_config_summary()
                except ImportError:
                    print("❌ 无法导入配置文件")
                    
            elif choice == "3":
                print("👋 再见!")
                break
                
            else:
                print("❌ 无效选择，请输入1、2或3")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
