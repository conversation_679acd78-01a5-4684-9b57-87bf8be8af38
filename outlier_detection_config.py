#!/usr/bin/env python3
"""
异常检测参数配置文件
可以通过修改这个文件来调整异常检测的参数
"""

# Isolation Forest 参数配置
ISOLATION_FOREST_CONFIG = {
    # contamination: 预期异常比例
    # 0.01 = 1% (非常严格，筛选很少)
    # 0.03 = 3% (较严格)
    # 0.04 = 4% (适中)
    # 0.05 = 5% (稍宽松)
    # 0.06 = 6% (当前设置，宽松)
    # 0.08 = 8% (原始设置，较宽松)
    # 0.10 = 10% (宽松，筛选较多)
    'contamination': 0.06,
    
    # n_estimators: 树的数量，越多越准确但速度越慢
    'n_estimators': 200,
    
    # max_samples: 每棵树使用的样本数量
    'max_samples': 'auto',
    
    # random_state: 随机种子，保证结果可重复
    'random_state': 42,
    
    # n_jobs: 并行处理的CPU核数，-1表示使用所有核
    'n_jobs': -1
}

# K-means 参数配置 (如果需要使用K-means)
KMEANS_CONFIG = {
    # n_clusters: 聚类数量，"auto"表示自动选择
    'n_clusters': "auto",
    
    # distance_threshold: 距离阈值倍数，越大筛选越少
    'distance_threshold': 2.5,
    
    # random_state: 随机种子
    'random_state': 42,
    
    # n_init: 初始化次数
    'n_init': 10,
    
    # max_iter: 最大迭代次数
    'max_iter': 300
}

# 行为特征配置
BEHAVIORAL_FEATURES = [
    "pose_Tx", "pose_Ty",           # 头部位置特征
    "AU17_r", "AU26_r"             # 面部动作单元特征
]

# 数据路径配置
DATA_PATHS = {
    'train_path': "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train",
    'val_path': "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation"
}

# 输出文件配置
OUTPUT_FILES = {
    'train_isolation': "train_isolation_outliers.csv",
    'val_isolation': "val_isolation_outliers.csv",
    'train_kmeans': "train_kmeans_outliers.csv",
    'val_kmeans': "val_kmeans_outliers.csv",
    'train_combined': "train_combined_outliers.csv",
    'val_combined': "val_combined_outliers.csv"
}

def get_contamination_recommendation(current_outlier_count, total_count, target_percentage=3.0):
    """
    根据当前筛选结果推荐新的contamination参数
    
    参数:
        current_outlier_count: 当前筛选出的异常数量
        total_count: 总样本数量
        target_percentage: 目标异常比例 (%)
    
    返回:
        推荐的contamination值
    """
    current_percentage = (current_outlier_count / total_count) * 100
    
    if current_percentage > target_percentage * 1.5:
        # 如果当前筛选太多，降低contamination
        new_contamination = max(0.01, ISOLATION_FOREST_CONFIG['contamination'] * 0.7)
        print(f"💡 当前筛选了 {current_percentage:.1f}%，建议降低contamination到 {new_contamination:.3f}")
    elif current_percentage < target_percentage * 0.5:
        # 如果当前筛选太少，提高contamination
        new_contamination = min(0.15, ISOLATION_FOREST_CONFIG['contamination'] * 1.3)
        print(f"💡 当前筛选了 {current_percentage:.1f}%，建议提高contamination到 {new_contamination:.3f}")
    else:
        new_contamination = ISOLATION_FOREST_CONFIG['contamination']
        print(f"✅ 当前筛选比例 {current_percentage:.1f}% 合适，保持contamination={new_contamination:.3f}")
    
    return new_contamination

def print_config_summary():
    """打印当前配置摘要"""
    print("📋 当前异常检测配置:")
    print("=" * 50)
    print(f"Isolation Forest contamination: {ISOLATION_FOREST_CONFIG['contamination']} ({ISOLATION_FOREST_CONFIG['contamination']*100:.1f}%)")
    print(f"K-means distance_threshold: {KMEANS_CONFIG['distance_threshold']}")
    print(f"使用特征: {BEHAVIORAL_FEATURES}")
    print(f"训练数据路径: {DATA_PATHS['train_path']}")
    print(f"验证数据路径: {DATA_PATHS['val_path']}")
    print("=" * 50)

if __name__ == "__main__":
    print_config_summary()
    
    # 示例：如何调整参数
    print("\n💡 参数调整建议:")
    print("- 如果筛选太多异常，降低 contamination (例如: 0.04 → 0.03 → 0.02)")
    print("- 如果筛选太少异常，提高 contamination (例如: 0.04 → 0.05 → 0.06)")
    print("- 修改此文件后重新运行异常检测即可")
