#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from data.data_loader import get_source_data

print('✅ 数据加载模块导入成功')

# 测试配置
config = {
    'behavioral_features': ['pose_Tx', 'pose_Ty', 'AU17_r', 'AU26_r'],
    'train_folder_path': '/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train',
    'test_folder_path': '/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation',
    'label_file': '/data/gsd/czx/project/Learning_status_detection/TCCT_Net-main/train_engagement_labels.csv'
}

print('🔧 开始测试特征级标准化...')
try:
    train_data, train_labels, test_data, test_labels = get_source_data(
        config['train_folder_path'],
        config['test_folder_path'], 
        config['label_file'],
        config['behavioral_features']
    )
    print('✅ 特征级标准化测试成功！')
    print(f'训练数据形状: {train_data.shape}')
    print(f'测试数据形状: {test_data.shape}')
except Exception as e:
    print(f'❌ 测试失败: {e}')
    import traceback
    traceback.print_exc()
